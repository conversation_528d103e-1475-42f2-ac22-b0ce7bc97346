import { Resolver, Query, Mutation, Args, Context, ID } from '@nestjs/graphql';
import { Logger } from '@nestjs/common';
import { AuthSharedService } from '../../auth-shared/auth.service';
import { User, AuthStatus, DecryptTokenInput, DecodeJwtInput, DecryptTokenResponse, DecodeJwtResponse } from '../types/auth.types';

@Resolver()
export class AuthResolver {
  private readonly logger = new Logger(AuthResolver.name);

  constructor(private readonly authService: AuthSharedService) {}

  @Mutation(() => Boolean, { name: 'logout', description: 'Logout user by clearing authentication cookies' })
  async logout(@Context() context: any): Promise<boolean> {
    this.logger.log('🔐 [AUTH RESOLVER] Logout mutation called');

    const response = context.res;

    if (!response) {
      this.logger.error('❌ [AUTH RESOLVER] Response context not available for logout');
      throw new Error('Response context not available');
    }

    try {
      // Clear access_token cookie (Fastify method with type assertion)
      (response as any).cookie('access_token', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        domain: process.env.COOKIE_DOMAIN || undefined,
        path: '/',
        expires: new Date(0) // Set to past date to clear
      });

      // Clear refresh_token cookie (Fastify method with type assertion)
      (response as any).cookie('refresh_token', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        domain: process.env.COOKIE_DOMAIN || undefined,
        path: '/',
        expires: new Date(0) // Set to past date to clear
      });

      this.logger.log('✅ [AUTH RESOLVER] Authentication cookies cleared successfully');
      return true;
    } catch (error) {
      this.logger.error(`❌ [AUTH RESOLVER] Error clearing cookies: ${error.message}`);
      throw new Error('Failed to logout');
    }
  }

  @Query(() => User, { name: 'meCookies', description: 'Get current user from encrypted cookies' })
  async getCurrentUserFromCookies(@Context() context: any): Promise<User> {
    this.logger.log('🔍 [AUTH RESOLVER] meCookies query called');

    const request = context.req;

    if (!request) {
      this.logger.error('❌ [AUTH RESOLVER] Request context not available');
      throw new Error('Request context not available');
    }

    // Log request details
    this.logger.log('🌐 [AUTH RESOLVER] Request details:');
    this.logger.log(`  - Origin: ${request.headers.origin || 'NONE'}`);
    this.logger.log(`  - User-Agent: ${request.headers['user-agent']?.substring(0, 50) || 'NONE'}...`);
    this.logger.log(`  - Cookie header present: ${!!request.headers.cookie}`);
    this.logger.log(`  - Raw cookie header: ${request.headers.cookie || 'NONE'}`);

    // Extract cookies from request
    const cookies = request.cookies || {};
    this.logger.log(`🍪 [AUTH RESOLVER] Parsed cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);
    this.logger.log('🍪 [AUTH RESOLVER] Cookie details:');
    this.logger.log(`  - access_token present: ${!!cookies.access_token}`);
    this.logger.log(`  - refresh_token present: ${!!cookies.refresh_token}`);
    this.logger.log(`  - access_token value: ${cookies.access_token ? `${cookies.access_token.substring(0, 20)}...` : 'MISSING'}`);
    this.logger.log(`  - refresh_token value: ${cookies.refresh_token ? `${cookies.refresh_token.substring(0, 20)}...` : 'MISSING'}`);

    if (!cookies.access_token && !cookies.refresh_token) {
      this.logger.warn('❌ [AUTH RESOLVER] No authentication cookies found');
      throw new Error('Authentication failed - no cookies');
    }

    try {
      this.logger.log('🔐 [AUTH RESOLVER] Attempting to validate user from cookies...');

      const user = await this.authService.authenticateFromCookies(cookies);

      if (!user) {
        this.logger.error('❌ [AUTH RESOLVER] User validation failed - no user returned');
        throw new Error('Authentication failed - no user');
      }

      this.logger.log(`✅ [AUTH RESOLVER] User authenticated successfully: ${user.email} (${user.role})`);
      return user;
    } catch (error) {
      this.logger.error(`❌ [AUTH RESOLVER] Authentication error: ${error.message}`);
      throw error;
    }
  }

  @Mutation(() => Boolean, { name: 'refreshAuth', description: 'Refresh authentication tokens' })
  async refreshAuthentication(@Context() context: any): Promise<boolean> {
    this.logger.log('GraphQL: Refreshing authentication');

    const cookies = context.req.cookies || {};
    return await this.authService.refreshAuthentication(cookies);
  }

  @Mutation(() => Boolean, { name: 'setAccessTokenCookie', description: 'Manually set access token cookie for debugging' })
  async setAccessTokenCookie(
    @Args('token', { type: () => String }) token: string,
    @Context() context: any
  ): Promise<boolean> {
    this.logger.log('🍪 [AUTH RESOLVER] Setting access token cookie manually');
    this.logger.log(`🔐 [AUTH RESOLVER] Token length: ${token.length} characters`);

    const response = context.reply;

    if (!response) {
      this.logger.error('❌ [AUTH RESOLVER] Response context not available');
      throw new Error('Response context not available');
    }

    try {
      // URL encode the token value
      const encodedToken = encodeURIComponent(token);
      this.logger.log(`🔗 [AUTH RESOLVER] URL encoded token length: ${encodedToken.length} characters`);

      // Set cookie with specified parameters using Fastify's cookie method
      (response as any).cookie('access_token', encodedToken, {
        domain: '.dev1.ngnair.com',
        path: '/',
        maxAge: 900, // 15 minutes in seconds
        sameSite: 'lax',
        httpOnly: false, // Allow JavaScript access for debugging
        secure: false // Allow over HTTP for local development
      });

      this.logger.log(`✅ [AUTH RESOLVER] Cookie set successfully with domain .dev1.ngnair.com`);
      this.logger.log(`🔍 [AUTH RESOLVER] Cookie settings: domain=.dev1.ngnair.com, path=/, maxAge=900, sameSite=lax`);

      return true;
    } catch (error) {
      this.logger.error(`❌ [AUTH RESOLVER] Failed to set cookie: ${error.message}`);
      return false;
    }
  }

  @Query(() => AuthStatus, { name: 'authStatus', description: 'Get authentication service status' })
  async getAuthStatus(): Promise<AuthStatus> {
    this.logger.log('GraphQL: Getting auth status');
    
    return {
      status: 'ok',
      service: 'auth-shared',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };
  }

  @Mutation(() => DecryptTokenResponse, { name: 'decryptToken', description: 'Decrypt encrypted token (testing)' })
  async decryptToken(@Args('input') input: DecryptTokenInput): Promise<DecryptTokenResponse> {
    this.logger.log('GraphQL: Decrypting token for testing');
    
    try {
      const decryptedToken = await this.authService.decryptTokenForTesting(input.token);
      
      // Try to determine token type
      let tokenType = 'unknown';
      try {
        const parsed = JSON.parse(decryptedToken);
        if (parsed.exp && parsed.iat) {
          tokenType = 'jwt';
        }
      } catch {
        // Not JSON, might be a plain token
        if (decryptedToken.includes('.')) {
          tokenType = 'jwt';
        }
      }
      
      return {
        decryptedToken,
        tokenType,
        success: true,
      };
    } catch (error) {
      return {
        decryptedToken: '',
        tokenType: 'unknown',
        success: false,
        error: error.message,
      };
    }
  }

  @Mutation(() => DecodeJwtResponse, { name: 'decodeJwt', description: 'Decode JWT token (testing)' })
  async decodeJwt(@Args('input') input: DecodeJwtInput): Promise<DecodeJwtResponse> {
    this.logger.log('GraphQL: Decoding JWT for testing');
    
    try {
      const jwt = require('jsonwebtoken');
      const decoded = jwt.decode(input.jwt, { complete: true });
      
      if (!decoded) {
        throw new Error('Invalid JWT format');
      }
      
      return {
        header: JSON.stringify(decoded.header),
        payload: JSON.stringify(decoded.payload),
        signature: decoded.signature || '',
        success: true,
      };
    } catch (error) {
      return {
        header: '',
        payload: '',
        signature: '',
        success: false,
        error: error.message,
      };
    }
  }

  @Query(() => User, { name: 'user', description: 'Get user by ID from external auth service' })
  async getUserById(
    @Args('id', { type: () => ID }) id: string,
    @Context() context: any
  ): Promise<User> {
    this.logger.log(`🔍 [AUTH RESOLVER] Getting user by ID: ${id}`);

    const request = context.req;

    if (!request) {
      this.logger.error('❌ [AUTH RESOLVER] Request context not available');
      throw new Error('Request context not available');
    }

    // Extract cookies from request
    const cookies = request.cookies || {};
    this.logger.log(`🍪 [AUTH RESOLVER] Cookies available: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    if (!cookies.access_token) {
      this.logger.warn('❌ [AUTH RESOLVER] No access token found in cookies');
      throw new Error('Authentication required - no access token');
    }

    try {
      this.logger.log('🔐 [AUTH RESOLVER] Fetching user from external auth service...');

      const user = await this.authService.getUserById(id, cookies);

      if (!user) {
        this.logger.error('❌ [AUTH RESOLVER] User not found');
        throw new Error('User not found');
      }

      this.logger.log(`✅ [AUTH RESOLVER] User retrieved successfully: ${user.email}`);
      return user;
    } catch (error) {
      this.logger.error(`❌ [AUTH RESOLVER] Error getting user by ID: ${error.message}`);
      throw error;
    }
  }

  @Query(() => [User], { name: 'users', description: 'Get all users from external auth service. Automatically URL-decodes cookies before forwarding to external service.' })
  async getAllUsers(@Context() context: any): Promise<User[]> {
    this.logger.log('🔍 [AUTH RESOLVER] Getting all users');

    const request = context.req;

    if (!request) {
      this.logger.error('❌ [AUTH RESOLVER] Request context not available');
      throw new Error('Request context not available');
    }

    // Extract cookies from request
    const cookies = request.cookies || {};
    this.logger.log(`🍪 [AUTH RESOLVER] Cookies available: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    if (!cookies.access_token) {
      this.logger.warn('❌ [AUTH RESOLVER] No access token found in cookies');
      throw new Error('Authentication required - no access token');
    }

    try {
      this.logger.log('🔐 [AUTH RESOLVER] Fetching users from external auth service...');

      const users = await this.authService.getAllUsers(cookies);

      this.logger.log(`✅ [AUTH RESOLVER] Users retrieved successfully: ${users.length} users`);
      return users;
    } catch (error) {
      this.logger.error(`❌ [AUTH RESOLVER] Error getting all users: ${error.message}`);
      throw error;
    }
  }
}
